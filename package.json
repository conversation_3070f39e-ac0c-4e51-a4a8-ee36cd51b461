{"version": "3.7.6", "private": true, "name": "react-todolist", "description": "To-do list made with React.", "author": "<EMAIL>", "license": "ISC", "main": "index.js", "scripts": {"install:global": "node ./bin/preinstall-global-dependencies.js", "install:clean": "rm -rf node_modules package-lock.json", "start": "webpack serve --mode development", "build": "webpack --mode production", "prettier:fix": "prettier --write \"./**/*.{css,scss,sass,json,js,jsx,cjs,mjs}\"", "eslint:fix": "eslint \"**/*.{json,js,jsx,cjs,mjs}\" --fix --ignore-path .eslintignore", "stylelint:fix": "stylelint \"**/*.{css,scss,sass}\" --fix --ignore-path .stylelintignore", "lint": "npm run prettier:fix && npm run eslint:fix && npm run stylelint:fix", "changelog:init": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "changelog:update": "standard-version --tag-prefix='' --release-commit-message-format 'ci(changelog): update files with the new version {{currentTag}}'", "changelog:update:gitflow": "sh ./bin/standar-version-updater-gitflow.sh", "deploy": "node gh-pages-deploy.mjs"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^2.3.0", "chalk": "^5.4.1", "core-js": "^3.43.0", "execa": "^9.6.0", "node-emoji": "^2.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/eslint-parser": "^7.25.0", "@babel/preset-env": "^7.25.0", "@babel/preset-react": "^7.25.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "babel-loader": "^9.1.0", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "css-loader": "^7.1.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-webpack-plugin": "^5.0.2", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.6", "prettier": "^3.6.1", "sass": "^1.89.2", "sass-loader": "^16", "standard-version": "^9.5.0", "style-loader": "^4.0.0", "stylelint": "^16.21.0", "stylelint-config-property-sort-order-smacss": "^10.0.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard": "^38.0.0", "stylelint-scss": "^6.12.1", "webpack": "^5.90.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "globalDependencies": {"@vue/cli": "^5.0.8", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "prettier": "^3.6.1", "standard-version": "^9.5.0"}, "engines": {"node": "20.18.0", "npm": "10.8.2"}}