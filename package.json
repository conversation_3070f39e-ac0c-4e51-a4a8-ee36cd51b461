{"version": "3.7.6", "private": true, "name": "vue-todolist", "description": "To-do list made with <PERSON><PERSON>.", "author": "<EMAIL>", "license": "ISC", "main": "index.js", "scripts": {"install:global": "node ./bin/preinstall-global-dependencies.js", "install:clean": "rm -rf node_modules package-lock.json", "preinstall": "npm run install:global", "postinstall": "npm run lint", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "prettier:fix": "prettier --write \"./**/*.{css,scss,sass,json,js,cjs,mjs,vue}\"", "eslint:fix": "vue-cli-service lint \"**/*.{json,js,cjs,mjs,vue}\" --fix --ignore-path .eslintignore", "stylelint:fix": "stylelint \"**/*.{css,scss,sass,vue}\" --fix --ignore-path .stylelintignore", "lint": "npm run prettier:fix && npm run eslint:fix && npm run stylelint:fix", "changelog:init": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "changelog:update": "standard-version --tag-prefix='' --release-commit-message-format 'ci(changelog): update files with the new version {{currentTag}}'", "changelog:update:gitflow": "sh ./bin/standar-version-updater-gitflow.sh", "deploy": "node gh-pages-deploy.mjs", "prepare": "husky"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^2.0.10", "chalk": "^5.4.1", "core-js": "^3.43.0", "execa": "^9.6.0", "node-emoji": "^2.2.0", "svg-symbol-sprite-loader": "^5.1.0", "vue": "^2.7.16", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-prettier": "^10.2.0", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsonc": "^2.20.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.2.0", "eslint-webpack-plugin": "^5.0.2", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "prettier": "^3.6.1", "sass": "^1.89.2", "sass-loader": "^16", "standard-version": "^9.5.0", "stylelint": "^16.21.0", "stylelint-config-property-sort-order-smacss": "^10.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-config-standard": "^38.0.0", "stylelint-scss": "^6.12.1", "stylelint-webpack-plugin": "^5.0.1", "vue-template-compiler": "^2.7.16"}, "globalDependencies": {"@vue/cli": "^5.0.8", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "prettier": "^3.6.1", "standard-version": "^9.5.0"}, "engines": {"node": "20.18.0", "npm": "10.8.2"}}