# For more information see: https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "saturday"
      time: "09:00"
      timezone: "Europe/Madrid"
    assignees:
      - beatrizsmerino
    commit-message:
      prefix: build
      prefix-development: build
      include: scope
    open-pull-requests-limit: 10
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "saturday"
      time: "09:00"
      timezone: "Europe/Madrid"
    assignees:
      - beatrizsmerino
    commit-message:
      prefix: build
      prefix-development: build
      include: scope
    open-pull-requests-limit: 10
