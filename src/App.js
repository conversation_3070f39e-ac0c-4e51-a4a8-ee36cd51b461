import React from 'react';
import { useSelector } from 'react-redux';
import { selectPageTitle } from './store/slices/appSlice';
import PageTitle from './components/Page/PageTitle';
import Dashboard from './components/Dashboard/Dashboard';
import UIMessage from './components/UI/UIMessage';
import './assets/scss/base/base-reset.scss';
import './assets/scss/base/base-global.scss';
import './assets/scss/base/base-fonts.scss';

const App = () => {
  const pageTitle = useSelector(selectPageTitle);

  return (
    <div className="app">
      <PageTitle title={pageTitle} />
      <Dashboard />
      <UIMessage>
        <p>This application uses the browser's Local Storage to store data.</p>
      </UIMessage>
    </div>
  );
};

export default App;
