import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  pageTitle: 'React ToDo List',
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setPageTitle: (state, action) => {
      state.pageTitle = action.payload;
    },
  },
});

export const { setPageTitle } = appSlice.actions;

// Selectors
export const selectPageTitle = (state) => state.app.pageTitle;

export default appSlice.reducer;
