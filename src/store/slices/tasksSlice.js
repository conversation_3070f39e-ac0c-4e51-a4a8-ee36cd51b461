import { createSlice } from '@reduxjs/toolkit';
import initialTasks from '../../assets/data/tasks.json';

const initialState = {
  list: initialTasks,
};

const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    addTask: (state, action) => {
      const taskNew = {
        id: action.payload.id,
        name: action.payload.name,
        status: {
          done: false,
          show: false,
        },
      };
      state.list.push(taskNew);
    },
    updateTask: (state, action) => {
      const taskIndex = state.list.findIndex(item => item.id === action.payload.id);
      if (taskIndex !== -1) {
        state.list[taskIndex] = action.payload;
      }
    },
    removeTask: (state, action) => {
      state.list = state.list.filter(item => item.id !== action.payload);
    },
    removeAllTasks: (state) => {
      state.list = [];
    },
    removeCompletedTasks: (state) => {
      state.list = state.list.filter(item => !item.status.done);
    },
    loadTasksFromStorage: (state, action) => {
      state.list = action.payload;
    },
  },
});

export const {
  addTask,
  updateTask,
  removeTask,
  removeAllTasks,
  removeCompletedTasks,
  loadTasksFromStorage,
} = tasksSlice.actions;

// Selectors
export const selectTaskList = (state) => state.tasks.list;
export const selectTotalTasks = (state) => state.tasks.list.length;
export const selectCompletedTasks = (state) => state.tasks.list.filter(item => item.status.done).length;
export const selectLastTask = (state) => {
  const list = state.tasks.list;
  return list.length > 0 ? list[list.length - 1] : null;
};

export default tasksSlice.reducer;
