const TASKS_STORAGE_KEY = 'tasks';

export const saveTasksToStorage = (tasks) => {
  try {
    localStorage.setItem(TASKS_STORAGE_KEY, JSON.stringify(tasks));
  } catch (error) {
    console.error('Error saving tasks to localStorage:', error);
  }
};

export const loadTasksFromStorage = () => {
  try {
    const tasks = localStorage.getItem(TASKS_STORAGE_KEY);
    return tasks ? JSON.parse(tasks) : null;
  } catch (error) {
    console.error('Error loading tasks from localStorage:', error);
    return null;
  }
};

export const removeTasksFromStorage = () => {
  try {
    localStorage.removeItem(TASKS_STORAGE_KEY);
  } catch (error) {
    console.error('Error removing tasks from localStorage:', error);
  }
};
