import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { selectTaskList } from '../../store/slices/tasksSlice';
import { loadTasksFromStorage } from '../../store/slices/tasksSlice';
import { saveTasksToStorage, loadTasksFromStorage as loadFromStorage } from '../../utils/localStorage';
import DashboardInfo from './DashboardInfo';
import DashboardContent from './DashboardContent';
import './Dashboard.scss';

const Dashboard = () => {
  const dispatch = useDispatch();
  const taskList = useSelector(selectTaskList);

  useEffect(() => {
    // Load tasks from localStorage on component mount
    const savedTasks = loadFromStorage();
    if (savedTasks) {
      dispatch(loadTasksFromStorage(savedTasks));
    }
  }, [dispatch]);

  useEffect(() => {
    // Save tasks to localStorage whenever taskList changes
    saveTasksToStorage(taskList);
  }, [taskList]);

  return (
    <div className="dashboard">
      <DashboardInfo />
      <DashboardContent />
    </div>
  );
};

export default Dashboard;
