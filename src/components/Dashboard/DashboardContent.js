import React, { useRef } from 'react';
import TaskList from '../Task/TaskList';
import TaskNew from '../Task/TaskNew';

const DashboardContent = () => {
  const taskListRef = useRef(null);

  const handleAddTask = () => {
    if (taskListRef.current && taskListRef.current.scrollToBottom) {
      taskListRef.current.scrollToBottom();
    }
  };

  return (
    <div className="dashboard-content">
      <TaskList ref={taskListRef} />
      <TaskNew onAddTask={handleAddTask} />
    </div>
  );
};

export default DashboardContent;
