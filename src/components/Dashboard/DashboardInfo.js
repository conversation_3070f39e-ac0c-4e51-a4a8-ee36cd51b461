import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { selectTotalTasks, selectCompletedTasks, removeAllTasks, removeCompletedTasks } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import UITag from '../UI/UITag';
import './DashboardInfo.scss';

const DashboardInfo = () => {
  const dispatch = useDispatch();
  const totalTasks = useSelector(selectTotalTasks);
  const completedTasks = useSelector(selectCompletedTasks);

  const handleRemoveAllTasks = () => {
    dispatch(removeAllTasks());
  };

  const handleRemoveCompletedTasks = () => {
    dispatch(removeCompletedTasks());
  };

  return (
    <div className="dashboard-info">
      <ul className="dashboard-info__list">
        <li className="dashboard-info__item">
          <UITag tagName="Tasks" tagValue={totalTasks} />
        </li>
        <li className="dashboard-info__item">
          <UITag tagName="Tasks Done" tagValue={completedTasks} />
        </li>
      </ul>
      <ul className="dashboard-info__list">
        {completedTasks !== 0 && (
          <li className="dashboard-info__item">
            <UIButton
              className="button--bg-color-error button--small"
              onClick={handleRemoveCompletedTasks}
            >
              <span className="button__icon">
                <i className="icon">
                  <FontAwesomeIcon icon={faTrash} />
                </i>
              </span>
              <span className="button__text">
                tasks done
              </span>
            </UIButton>
          </li>
        )}
        <li className="dashboard-info__item">
          <UIButton
            className="button--bg-color-error button--small"
            onClick={handleRemoveAllTasks}
          >
            <span className="button__icon">
              <i className="icon">
                <FontAwesomeIcon icon={faTrash} />
              </i>
            </span>
            <span className="button__text">
              tasks
            </span>
          </UIButton>
        </li>
      </ul>
    </div>
  );
};

export default DashboardInfo;
