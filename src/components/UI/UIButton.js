import React from 'react';
import './UIButton.scss';

const UIButton = ({
  href,
  to,
  value,
  className = '',
  onClick,
  children,
  ...props
}) => {
  const handleClick = (event) => {
    if (onClick) {
      onClick(event);
    }
  };

  const getTag = () => {
    if (href) {
      return 'a';
    } else if (value) {
      return 'input';
    } else if (to) {
      // For router links, we'll use a regular link for now
      return 'a';
    }
    return 'button';
  };

  const Tag = getTag();
  const buttonProps = {
    className: `button ${className}`,
    onClick: handleClick,
    ...props
  };

  if (href) {
    buttonProps.href = href;
  }
  if (to) {
    buttonProps.href = to;
  }
  if (value) {
    buttonProps.value = value;
  }

  return (
    <Tag {...buttonProps}>
      {children}
    </Tag>
  );
};

export default UIButton;


