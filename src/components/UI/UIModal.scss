@import '../../assets/scss/abstracts/abstracts-variables';

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba($color-black, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: $color-white;
  padding: 2rem;
  border-radius: 0.5rem;
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: $color-gray;
  
  &:hover {
    color: $color-black;
  }
}
