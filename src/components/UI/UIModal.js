import React from 'react';
import './UIModal.scss';

const UIModal = ({ isOpen, onClose, triggerButton, children }) => {
  if (!isOpen) {
    return triggerButton;
  }

  return (
    <>
      {triggerButton}
      <div className="modal-overlay" onClick={onClose}>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
          <button className="modal-close" onClick={onClose}>×</button>
          {children}
        </div>
      </div>
    </>
  );
};

export default UIModal;
