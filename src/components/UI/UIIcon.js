import React from 'react';

const UIIcon = ({ name, className = '' }) => {
  // For now, we'll use a simple placeholder for icons
  // In a real implementation, you would load the actual SVG icons
  const getIconContent = () => {
    switch (name) {
      case 'emptyTasks':
        return (
          <svg className={`icon ${className}`} viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        );
      default:
        return (
          <svg className={`icon ${className}`} viewBox="0 0 24 24" fill="currentColor">
            <circle cx="12" cy="12" r="10"/>
          </svg>
        );
    }
  };

  return getIconContent();
};

export default UIIcon;
