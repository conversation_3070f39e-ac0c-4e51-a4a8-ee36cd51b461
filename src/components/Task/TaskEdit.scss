@import '../../assets/scss/abstracts/abstracts-variables';

.task-edit {
  min-width: 300px;

  h3 {
    margin-bottom: 1rem;
    color: $color-brand-3;
    font-size: 1.8rem;
  }

  &__field {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  &__input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 0.2rem solid $color-brand-3;
    border-radius: 0.5rem;
    outline: none;
    color: $color-brand-3;
    font-size: 1.6rem;
    font-weight: 600;

    &::placeholder {
      color: $color-gray;
      font-weight: 400;
    }

    &:focus {
      border-color: $color-brand-2;
    }
  }

  &__button-save {
    align-self: flex-end;
    padding: 0.8rem 1.5rem;
  }
}
