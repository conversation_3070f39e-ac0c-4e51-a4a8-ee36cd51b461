import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { useSelector } from 'react-redux';
import { selectTaskList, selectTotalTasks } from '../../store/slices/tasksSlice';
import TaskPreview from './TaskPreview';
import UIIcon from '../UI/UIIcon';
import './TaskList.scss';

const TaskList = forwardRef((props, ref) => {
  const taskListWrapperRef = useRef(null);
  const taskList = useSelector(selectTaskList);
  const totalTasks = useSelector(selectTotalTasks);

  const scrollToBottom = () => {
    if (taskListWrapperRef.current) {
      const element = taskListWrapperRef.current;
      element.scrollTop = element.scrollHeight;
    }
  };

  useImperativeHandle(ref, () => ({
    scrollToBottom
  }));

  useEffect(() => {
    scrollToBottom();
  }, []);

  return (
    <div ref={taskListWrapperRef} className="task-list__wrapper">
      {totalTasks !== 0 ? (
        <ul className="task-list">
          {taskList.map((item) => (
            <li key={item.id} className="task-list__item">
              <TaskPreview taskItem={item} />
            </li>
          ))}
        </ul>
      ) : (
        <div className="task-list__message">
          <p>Your task list is empty</p>
          <UIIcon name="emptyTasks" />
        </div>
      )}
    </div>
  );
});

TaskList.displayName = 'TaskList';

export default TaskList;
