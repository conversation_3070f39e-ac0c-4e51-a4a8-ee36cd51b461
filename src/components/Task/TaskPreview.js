import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';
import { removeTask, updateTask } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import UIModal from '../UI/UIModal';
import TaskEdit from './TaskEdit';
import './TaskPreview.scss';

const TaskPreview = ({ taskItem }) => {
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false);

  const handleChangeTaskDone = () => {
    const updatedTask = {
      ...taskItem,
      status: {
        ...taskItem.status,
        done: !taskItem.status.done
      }
    };
    dispatch(updateTask(updatedTask));
  };

  const handleRemoveTask = () => {
    dispatch(removeTask(taskItem.id));
  };

  const handleShowModal = () => {
    setShowModal(true);
  };

  const handleHideModal = () => {
    setShowModal(false);
  };

  const handleEditTask = () => {
    setShowModal(false);
  };

  return (
    <div className={`task-preview ${taskItem.status.done ? 'is-done' : ''} ${showModal ? 'is-show' : ''}`}>
      <UIButton
        className="task-preview__button-done button--icon"
        onClick={handleChangeTaskDone}
      >
        <span className="button__icon">
          <i className="icon">
            <FontAwesomeIcon icon={faCheckCircle} />
          </i>
        </span>
      </UIButton>
      <div className="task-preview__name">
        <p>{taskItem.name}</p>
      </div>
      <ul className="task-preview__tools">
        <li>
          <UIModal
            isOpen={showModal}
            onClose={handleHideModal}
            triggerButton={
              <UIButton
                className="task-preview__button-edit button--icon"
                onClick={handleShowModal}
              >
                <span className="button__icon">
                  <i className="icon">
                    <FontAwesomeIcon icon={faEdit} />
                  </i>
                </span>
              </UIButton>
            }
          >
            <TaskEdit
              task={taskItem}
              onEditTask={handleEditTask}
            />
          </UIModal>
        </li>
        <li>
          <UIButton
            className="task-preview__button-remove button--icon"
            onClick={handleRemoveTask}
          >
            <span className="button__icon">
              <i className="icon">
                <FontAwesomeIcon icon={faTrash} />
              </i>
            </span>
          </UIButton>
        </li>
      </ul>
    </div>
  );
};

export default TaskPreview;
