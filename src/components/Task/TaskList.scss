@import '../../assets/scss/abstracts/abstracts-variables';

.task-list {
  display: flex;
  position: relative;
  flex-direction: column;
  list-style: none;

  &__wrapper {
    height: calc(5.6rem * 3);
    margin: 2rem;
    overflow-y: scroll;
  }

  &__item {
    padding: 0.5rem;

    /* Vue animation 'task-list' for the item transition */
    &.task-list-enter-active,
    &.task-list-leave-active {
      transform-origin: left center;
      transition:
        opacity 0.3s,
        transform 0.3s;
    }

    &.task-list-enter,
    &.task-list-leave-to {
      transform: scale(0.5);
      opacity: 0;
    }

    &.task-list-leave-active {
      position: absolute;
    }

    &.task-list-move {
      transition: transform 0.4s linear 0.3;
    }
  }

  &__message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $color-light;
    font-size: 2rem;
    line-height: 100%;
    text-align: center;

    > * {
      &:not(:last-child) {
        margin-bottom: 1rem;
      }
    }

    .icon {
      width: 15rem;
      height: 10rem;
    }
  }
}
