import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateTask } from '../../store/slices/tasksSlice';
import UIButton from '../UI/UIButton';
import './TaskEdit.scss';

const TaskEdit = ({ task, onEditTask }) => {
  const dispatch = useDispatch();
  const [taskName, setTaskName] = useState(task.name);

  const handleSaveTask = () => {
    const trimmedName = taskName.trim();
    
    if (trimmedName !== '') {
      const updatedTask = {
        ...task,
        name: trimmedName,
      };
      
      dispatch(updateTask(updatedTask));
      
      if (onEditTask) {
        onEditTask();
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSaveTask();
    }
  };

  return (
    <div className="task-edit">
      <h3>Edit Task</h3>
      <div className="task-edit__field">
        <input
          value={taskName}
          onChange={(e) => setTaskName(e.target.value)}
          className="task-edit__input"
          type="text"
          placeholder="Task name"
          autoFocus
          onKeyPress={handleKeyPress}
        />
        <UIButton
          className="task-edit__button-save button--bg-color-3"
          onClick={handleSaveTask}
        >
          Save
        </UIButton>
      </div>
    </div>
  );
};

export default TaskEdit;
